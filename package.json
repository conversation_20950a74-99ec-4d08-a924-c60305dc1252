{"name": "e-commerce-site", "version": "0.1.0", "private": true, "dependencies": {"bootstrap": "^4.3.1", "react": "^16.8.6", "react-dom": "^16.8.6", "react-icons": "^5.0.1", "react-responsive": "^9.0.2", "react-router-dom": "^5.0.1", "react-scripts": "3.0.1", "styled-components": "^4.3.2"}, "scripts": {"start": "react-scripts start --openssl-legacy-provider", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.33", "tailwindcss": "^3.4.1"}}